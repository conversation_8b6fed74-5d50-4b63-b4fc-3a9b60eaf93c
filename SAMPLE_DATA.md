# Sample Data for sit_chat Database

This document describes the sample data that has been populated into the `sit_chat` MongoDB database.

## Database Connection

- **URI**: `***********************************************************************`
- **Database**: `sit_chat`
- **Collections**: `threads`, `messages`

## Sample Users

The following 6 users are used throughout the sample data:

1. **<PERSON>** (@jdoe) - `userId`: UUID - Admin/Creator role
2. **<PERSON>** (@asmith) - `userId`: UUID - Developer
3. **<PERSON>** (@b<PERSON><PERSON><PERSON>) - `userId`: UUID - Developer
4. **<PERSON>** (@cdavis) - `userId`: UUID - Student
5. **<PERSON>** (@dwilson) - `userId`: UUID - Student
6. **<PERSON>** (@ebrown) - `userId`: UUID - User

Each user has a generated avatar using `ui-avatars.com` service.

## Threads (5 total)

### 1. General Discussion (Public)

- **Participants**: All 6 users
- **Created by**: <PERSON>
- **Description**: A place for everyone to chat about anything and everything
- **Max <PERSON>**: 100
- **Messages**: 5 (including 1 reply)

### 2. Project Team Alpha (Private)

- **Participants**: John Doe, Alice Smith, Bob Johnson
- **Created by**: John Doe
- **Description**: Private discussions for the Alpha project development team
- **Max Participants**: 10
- **Messages**: 4

### 3. Study Group - JavaScript (Public)

- **Participants**: Alice Smith, Bob Johnson, Carol Davis, David Wilson
- **Created by**: Bob Johnson
- **Description**: Let's study JavaScript together! Share resources and ask questions
- **Max Participants**: 50
- **Messages**: 4

### 4. Announcements (Public)

- **Participants**: All 6 users
- **Created by**: John Doe
- **Description**: Official announcements and important updates
- **Max Participants**: 1000
- **Messages**: 2 (1 system message, 1 text)

### 5. Random Chat (Public)

- **Participants**: John Doe, Carol Davis, David Wilson, Emma Brown
- **Created by**: Carol Davis
- **Description**: Chat about anything random - pets, weather, food, you name it!
- **Max Participants**: 25
- **Messages**: 4

## Messages (19 total)

### Message Types

- **Text Messages**: 18
- **System Messages**: 1

### Features Demonstrated

- **Read Receipts**: Most messages have read receipts from various users
- **Attachments**: 1 message includes a file attachment (dog photo)
- **Replies**: 1 message is a reply to another message
- **Timestamps**: Messages span across different days with realistic timing
- **Message Types**: Mix of regular text and system messages
- **Varied Content**: Conversations about work, study, announcements, and casual chat

### Sample Conversations

#### General Discussion

- Welcome message and responses
- Weather discussion with replies
- Casual conversation flow

#### Project Team Alpha (Private)

- Sprint planning discussion
- Progress updates
- Meeting scheduling

#### Study Group

- JavaScript learning content
- Technical explanations with code examples
- Q&A format discussions

#### Announcements

- System maintenance notification
- Feature release announcement

#### Random Chat

- Casual welcome
- Lost pet discussion with image attachment
- Happy resolution update

## Data Characteristics

### Realistic Timing

- Messages are distributed across 10 days in the past
- Conversations show natural flow and timing
- Read receipts follow logical patterns

### User Engagement

- Different participation levels per room
- Natural conversation patterns
- Mix of question/answer formats

### Technical Features

- All schema fields are utilized
- Proper MongoDB ObjectId references
- UUID v4 for user identification
- Comprehensive indexing support

## Usage

To regenerate this sample data:

```bash
node populate-chat-data.js
```

This will clear existing data and repopulate with fresh sample data including new UUIDs for users.

## Queries for Testing

### Get all threads with participant count:

```javascript
db.threads.find({}, { name: 1, isPrivate: 1, participants: 1 }).pretty();
```

### Get messages from a specific thread:

```javascript
db.messages
  .find({ threadId: ObjectId('YOUR_CHATROOM_ID') })
  .sort({ timestamp: 1 });
```

### Find messages with attachments:

```javascript
db.messages.find({ 'attachments.0': { $exists: true } });
```

### Get unread messages for a user:

```javascript
db.messages.find({ 'readBy.user.userId': { $ne: 'USER_UUID' } });
```

### Search messages by content:

```javascript
db.messages.find({ $text: { $search: 'javascript' } });
```
