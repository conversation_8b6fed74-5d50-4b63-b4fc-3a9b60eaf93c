# MongoDB Setup for Threads Application

This directory contains the MongoDB configuration, schemas, and migration system for the threads application.

## Overview

The threads application uses MongoDB to store:

- **Threads**: Thread information including participants, settings, and metadata
- **Messages**: Individual thread messages with content, timestamps, and relationships

## Database Schema

### Threads Schema - Entity Relationship Diagram

```mermaid
erDiagram
    Thread {
        string id PK
        string name
        string description
        string createdBy
        boolean isPrivate
        int maxParticipants
        date createdAt
        date updatedAt
    }

    Message {
        string id PK
        string threadId FK
        string userId
        string content
        string messageType
        date timestamp
        date editedAt
        boolean isDeleted
        string replyTo FK
    }

    ThreadUser {
        string userId PK
        string username
        string displayName
        string avatar
    }

    MessageAttachment {
        string id PK
        string messageId FK
        string fileName
        string fileUrl
        int fileSize
        string mimeType
    }

    Thread ||--o{ Message : contains
    Message ||--o| Message : replies_to
    Thread ||--o{ ThreadUser : has_participants
    Message ||--o{ MessageAttachment : has_attachments
```

### Threads Collection

Each document represents a thread.

- `name`: Room name (required, indexed, trimmed, max 100 characters)
- `description`: Optional room description (trimmed, max 500 characters)
- `participants`: Array of user ObjectIds (indexed)
- `createdBy`: User ObjectId who created the room (indexed)
- `isPrivate`: Boolean flag for private rooms (indexed, default: false)
- `maxParticipants`: Maximum number of participants (min: 2, max: 1000, default: 100)
- `createdAt`, `updatedAt`: Timestamps (auto-managed)

### Messages Collection

Each document represents a message sent in a thread.

- `threadId`: Reference to thread (indexed)
- `userId`: Reference to user who sent the message (UUIDv4, indexed)
- `content`: Message content (required, trimmed, max 5000 characters, text indexed for search)
- `messageType`: Type of message - "text", "image", "file", "system" (indexed, default: "text")
- `timestamp`: When message was sent (indexed, default: current time)
- `editedAt`: When message was last edited (optional)
- `isDeleted`: Soft delete flag (indexed, default: false)
- `replyTo`: Reference to another message (optional)
- `attachments`: Array of file attachments (optional, default: empty array)
- `readBy`: Array of read receipts with userId and timestamp (default: empty array)

## Indexes

The following indexes are automatically created for optimal performance:

### Threads

- `name` (single)
- `participants` (single)
- `createdBy` (single)
- `isPrivate` (single)
- `createdAt` (single, descending)
- `participants + isPrivate` (compound)
- `createdBy + createdAt` (compound)

### Messages

- `threadId + timestamp` (compound, descending)
- `userId + timestamp` (compound, descending)
- `threadId + messageType` (compound)
- `threadId + isDeleted + timestamp` (compound)
- `replyTo` (single)
- `content` (text index for search)
