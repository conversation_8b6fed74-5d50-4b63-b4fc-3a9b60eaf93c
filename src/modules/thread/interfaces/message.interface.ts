import { Types } from 'mongoose';

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system',
}

export interface IMessageUser {
  userId: string; // UUIDv4
  username: string;
  displayName: string;
  avatar?: string;
  role?: string;
}

export interface IMessage {
  threadId: Types.ObjectId;
  user: IMessageUser;
  content: string;
  messageType: MessageType;
  timestamp: Date;
  editedAt?: Date;
  isDeleted: boolean;
  replyTo?: Types.ObjectId;
  attachments?: IMessageAttachment[];
  readBy: IMessageReadStatus[];
}

export interface IMessageAttachment {
  fileName?: string;
  fileUrl: string;
  fileSize?: number;
  mimeType?: string;
}

export interface IMessageReadStatus {
  user: IMessageUser;
  readAt: Date;
}

export interface ICreateMessageDto {
  threadId: string;
  content: string;
  messageType?: MessageType;
  replyTo?: string;
  attachments?: IMessageAttachment[];
}

export interface IUpdateMessageDto {
  content?: string;
  attachments?: IMessageAttachment[];
}

export interface IMarkMessageReadDto {
  messageId: string;
  userId: string;
}

export interface IGetMessagesQuery {
  threadId: string;
  page?: number;
  limit?: number;
  before?: string; // message ID for pagination
  after?: string; // message ID for pagination
}
