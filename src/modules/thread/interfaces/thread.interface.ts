export interface IChatroomUser {
  userId: string; // UUIDv4
  username: string;
  displayName: string;
  avatar?: string;
}

export interface IChatroom {
  name: string;
  description?: string;
  participants: IChatroomUser[]; // User objects array
  createdBy: string; // UUIDv4
  isPrivate: boolean;
  maxParticipants?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateChatroomDto {
  name: string;
  description?: string;
  participants?: string[];
  isPrivate?: boolean;
  maxParticipants?: number;
}

export interface IUpdateChatroomDto {
  name?: string;
  description?: string;
  isPrivate?: boolean;
  maxParticipants?: number;
}

export interface IJoinChatroomDto {
  chatroomId: string;
  userId: string;
}

export interface ILeaveChatroomDto {
  chatroomId: string;
  userId: string;
}
