export interface IThreadUser {
  userId: string; // UUIDv4
  username: string;
  displayName: string;
  avatar?: string;
}

export interface IThread {
  name: string;
  description?: string;
  participants: IThreadUser[]; // User objects array
  createdBy: string; // UUIDv4
  isPrivate: boolean;
  maxParticipants?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateThreadDto {
  name: string;
  description?: string;
  participants?: string[];
  isPrivate?: boolean;
  maxParticipants?: number;
}

export interface IUpdateThreadDto {
  name?: string;
  description?: string;
  isPrivate?: boolean;
  maxParticipants?: number;
}

export interface IJoinThreadDto {
  threadId: string;
  userId: string;
}

export interface ILeaveThreadDto {
  threadId: string;
  userId: string;
}
