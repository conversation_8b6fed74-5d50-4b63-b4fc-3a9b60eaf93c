import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { IThread, IThreadUser } from '../interfaces/thread.interface';

@Schema({
  timestamps: true,
  collection: 'threads',
})
export class Thread extends Document implements IThread {
  @Prop({
    required: true,
    trim: true,
    maxlength: 100,
    index: true,
  })
  name: string;

  @Prop({
    trim: true,
    maxlength: 500,
  })
  description?: string;

  @Prop({
    type: [
      {
        userId: { type: String, required: true }, // UUIDv4
        username: { type: String, required: true },
        displayName: { type: String, required: true },
        avatar: { type: String, required: false },
      },
    ],
    default: [],
    index: true,
  })
  participants: IThreadUser[];

  @Prop({
    type: String, // UUIDv4
    required: true,
    index: true,
  })
  createdBy: string;

  @Prop({
    type: Boolean,
    default: false,
    index: true,
  })
  isPrivate: boolean;

  @Prop({
    type: Number,
    min: 2,
    max: 1000,
    default: 100,
  })
  maxParticipants?: number;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const ThreadSchema = SchemaFactory.createForClass(Thread);

// Indexes for better performance
ThreadSchema.index({ name: 1 });
ThreadSchema.index({ 'participants.userId': 1 });
ThreadSchema.index({ createdBy: 1 });
ThreadSchema.index({ isPrivate: 1 });
ThreadSchema.index({ createdAt: -1 });

// Compound indexes
ThreadSchema.index({ 'participants.userId': 1, isPrivate: 1 });
ThreadSchema.index({ createdBy: 1, createdAt: -1 });

// Virtual for participant count
ThreadSchema.virtual('participantCount').get(function () {
  return this.participants.length;
});

// Ensure virtuals are included in JSON output
ThreadSchema.set('toJSON', { virtuals: true });
ThreadSchema.set('toObject', { virtuals: true });

export type ThreadDocument = Thread & Document;
