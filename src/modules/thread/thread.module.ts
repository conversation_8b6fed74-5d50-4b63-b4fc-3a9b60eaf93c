import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Thread, ThreadSchema } from './schemas/thread.schema';
import { Message, MessageSchema } from './schemas/message.schema';
import { MongodbModule } from '@database/mongodb/mongodb.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Thread.name, schema: ThreadSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
    MongodbModule,
  ],
  providers: [],
  controllers: [],
  exports: [MongooseModule, MongodbModule],
})
export class ThreadModule {}
