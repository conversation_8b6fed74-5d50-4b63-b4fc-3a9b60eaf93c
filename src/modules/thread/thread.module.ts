import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Chatroom, ChatroomSchema } from './schemas/thread.schema';
import { Message, MessageSchema } from './schemas/message.schema';
import { MongodbModule } from '@database/mongodb/mongodb.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Chatroom.name, schema: ChatroomSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
    MongodbModule,
  ],
  providers: [],
  controllers: [],
  exports: [MongooseModule, MongodbModule],
})
export class ChatModule {}
