import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { createClient } from 'redis';
import { createAdapter } from '@socket.io/redis-adapter';
// import { joinRoomWSController } from '../modules/chat/useCases/joinRoom';
// import { sendMessageWSController } from '../modules/chat/useCases/sendMessage';

@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  path: '/api/socket.io',
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  async onModuleInit() {
    const redisHost = process.env.REDIS_HOST || 'localhost';
    const redisPort = process.env.REDIS_PORT || '6379';
    const redisPassword = process.env.REDIS_PASSWORD || undefined;
    const redisUrl = `redis://${redisHost}:${redisPort}`;
    const pubClient = createClient({
      url: redisUrl,
      password: redisPassword,
    });
    const subClient = pubClient.duplicate();

    await pubClient.connect();
    await subClient.connect();

    this.server.adapter(createAdapter(pubClient, subClient));
    console.log('Redis adapter initialized');
    console.log('[Server]: Socket.io Server started with Redis Pub/Sub!');
  }

  handleConnection(client: Socket) {
    console.log(`Socket connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Socket disconnected: ${client.id}`);
  }

  @SubscribeMessage('joinRoom')
  handleJoinRoom(@MessageBody() data: any, client: Socket) {
    // return joinRoomWSController.execute(this.server, client, data);
  }

  @SubscribeMessage('sendMessage')
  handleSendMessage(@MessageBody() data: any, client: Socket) {
    // return sendMessageWSController.execute(this.server, client, data);
  }
}
